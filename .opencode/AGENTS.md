# Bachelor Thesis Writing Project - OpenCode Configuration

This project is dedicated to writing a Computer Science Bachelor thesis using AI-assisted tools and workflows. The entire thesis is written in LaTeX and follows academic standards for CS research.

## Project Structure

- `thesis/` - Main thesis directory containing all LaTeX files and content
- `thesis/.context/` - Research materials, agent thoughts, guidelines, and project metadata
- `thesis/sections/` - Individual thesis sections (introduction, methodology, results, etc.)
- `thesis/figures/` - Images, diagrams, and visual content
- `thesis/references/` - Bibliography and reference materials
- `.opencode/` - OpenCode configuration with specialized agents and tools

## Academic Writing Standards

### LaTeX Requirements
- Use proper LaTeX document structure with documentclass{article} or {report}
- Implement consistent formatting throughout all sections
- Use proper sectioning: \section{}, \subsection{}, \subsubsection{}
- Include proper figure and table environments with captions
- Use BibTeX for all references and citations
- Maintain consistent mathematical notation and formatting

### Content Standards
- Follow IMRaD structure: Introduction, Methodology, Results, and Discussion
- Each section should have clear objectives and logical flow
- Use formal academic language and third-person perspective
- Support all claims with proper citations
- Include comprehensive literature review
- Provide detailed methodology that enables reproduction
- Present results objectively with proper analysis
- Draw conclusions based on evidence presented

### Citation and Reference Standards
- Use IEEE or ACM citation style as appropriate for CS field
- Cite recent papers (preferably within last 5 years for current research)
- Include seminal works that established the field
- Properly attribute all ideas, methods, and findings to original authors
- Maintain consistent citation format throughout
- Include DOI links where available

## Agent Coordination Rules

### Primary Agents
- **thesis-writer**: Main writing agent for content creation and editing
- **research-coordinator**: Manages research activities and literature review

### Subagent Specialization
- **literature-reviewer**: Focuses on academic paper analysis and synthesis
- **methodology-expert**: Handles technical implementation and experimental design
- **quality-assurance**: Reviews content for academic standards and consistency
- **reference-manager**: Manages citations and bibliography
- **latex-specialist**: Handles LaTeX formatting and document structure
- **section-writer**: Writes individual sections with proper academic structure

### Workflow Coordination
1. Research phase: Use @research-coordinator and @literature-reviewer
2. Planning phase: Create detailed outline with @methodology-expert
3. Writing phase: Use @section-writer for individual sections
4. Review phase: Apply @quality-assurance for comprehensive review
5. Formatting phase: Use @latex-specialist for final formatting
6. Reference phase: Use @reference-manager for citation management

## File Organization Rules

### Context Management
- Save all research findings to `thesis/.context/research/`
- Store agent thoughts and decisions in `thesis/.context/thoughts/`
- Maintain project guidelines in `thesis/.context/guidelines/`
- Keep outline and structure in `thesis/.context/outline.md`

### Version Control
- Commit frequently with descriptive messages
- Use semantic versioning for major milestones
- Tag important versions (draft submissions, revisions)
- Maintain backup copies of critical sections

### Quality Assurance
- Run quality checks before each major milestone
- Verify all citations and references
- Check for consistency across sections
- Ensure LaTeX compiles without errors
- Review for plagiarism and originality

## Critical Instructions

### Research Integrity
- NEVER plagiarize or copy content without proper attribution
- Always verify facts and claims through multiple sources
- Maintain detailed records of all sources and references
- Use original analysis and interpretation of existing work

### Academic Standards
- Follow university-specific thesis requirements
- Adhere to field-specific conventions and standards
- Maintain professional and formal tone throughout
- Ensure logical flow and coherent argumentation

### Technical Requirements
- LaTeX must compile without errors or warnings
- All figures and tables must be properly referenced
- Mathematical notation must be consistent and correct
- Code listings must be properly formatted and documented

## Emergency Protocols

If any agent encounters issues:
1. Document the problem in `thesis/.context/thoughts/issues.md`
2. Consult with @quality-assurance for guidance
3. Refer to academic writing guidelines
4. Seek clarification on requirements before proceeding

## Success Metrics

- Thesis compiles successfully in LaTeX
- All sections meet academic writing standards
- Citations are complete and properly formatted
- Content is original and properly attributed
- Structure follows established academic conventions
- Quality meets bachelor thesis requirements
