# Agent Configuration Directory

This directory contains individual Markdown files for each agent in the thesis writing system. Each agent is defined in its own file for easier review, editing, and maintenance.

## Agent Files

### Primary Agents
- **[thesis-writer.md](./thesis-writer.md)** - Main writing agent for thesis content
- **[research-coordinator.md](./research-coordinator.md)** - Research management and coordination

### Subagents
- **[literature-reviewer.md](./literature-reviewer.md)** - Literature analysis and synthesis
- **[methodology-expert.md](./methodology-expert.md)** - Research methodology and experimental design
- **[quality-assurance.md](./quality-assurance.md)** - Quality review and academic standards
- **[reference-manager.md](./reference-manager.md)** - Citation and bibliography management
- **[latex-specialist.md](./latex-specialist.md)** - LaTeX formatting and document structure
- **[section-writer.md](./section-writer.md)** - Individual section writing and development

## Agent File Structure

Each agent file follows this structure:

```markdown
# Agent Name

Brief description of the agent's purpose.

## Configuration
- **Mode**: primary/subagent
- **Model**: AI model used
- **Temperature**: Creativity level
- **Prompt**: Link to detailed prompt file

## Tools
List of available tools and permissions

## Permissions
Access control settings

## Purpose
Detailed description of agent capabilities

## Usage
How and when to use this agent

## Collaboration
How this agent works with others
```

## Editing Agents

To modify an agent:

1. **Edit the Markdown file** directly (e.g., `thesis-writer.md`)
2. **Update configuration** as needed (model, temperature, tools, permissions)
3. **Modify the prompt reference** if you change the prompt file
4. **Test the changes** with `opencode agent list`

## Adding New Agents

To add a new agent:

1. **Create new Markdown file** in this directory
2. **Follow the standard structure** shown above
3. **Add to opencode.json** in the agent array: `"./agent/new-agent.md"`
4. **Create corresponding prompt** in `../prompts/new-agent.md`

## Agent Relationships

### Primary Agents
- Can be accessed directly by name (e.g., `thesis-writer`)
- Handle main workflows and coordinate with subagents
- Have broader permissions and tool access

### Subagents
- Called with `@` prefix (e.g., `@literature-reviewer`)
- Specialized for specific tasks
- More restricted permissions for focused expertise
- Work under direction of primary agents

## Configuration Reference

### Mode Options
- `primary`: Direct user interaction, broad capabilities
- `subagent`: Specialized tasks, called by other agents

### Model Options
- `github-copilot/gpt-4.1`: High-quality reasoning and writing
- `anthropic/claude-3-5-sonnet-20241022`: Alternative high-quality model
- `openai/gpt-4-turbo`: OpenAI's advanced model

### Temperature Settings
- `0.1`: Very focused, deterministic (quality assurance, references)
- `0.2`: Focused with slight creativity (research, methodology)
- `0.3`: Balanced creativity and focus (writing, sections)

### Tool Categories
- **File Operations**: `write`, `edit`, `read`
- **System**: `bash`, `grep`, `glob`
- **Research**: `tavily_*`, `arxiv_*`, `paper_search_*`
- **Web**: `webfetch`

### Permission Levels
- `allow`: Full access
- `ask`: Requires confirmation
- `deny`: No access

## Best Practices

### Agent Design
- **Single responsibility**: Each agent has a clear, focused purpose
- **Appropriate permissions**: Minimal necessary access for security
- **Clear collaboration**: Well-defined relationships between agents
- **Consistent naming**: Descriptive, hyphenated names

### Configuration Management
- **Version control**: Track changes to agent configurations
- **Documentation**: Keep agent purposes and usage clear
- **Testing**: Verify agent behavior after changes
- **Backup**: Maintain copies of working configurations

### Prompt Management
- **Separate files**: Keep prompts in dedicated files for easier editing
- **Consistent references**: Use relative paths for portability
- **Version alignment**: Ensure agent configs match prompt capabilities
- **Regular updates**: Keep prompts current with agent evolution

## Migration from JSON

This directory represents a migration from inline JSON agent definitions to individual Markdown files. Benefits include:

- **Easier editing**: Direct file editing vs JSON manipulation
- **Better organization**: One file per agent vs single large file
- **Improved readability**: Markdown formatting vs JSON structure
- **Version control**: Granular tracking of agent changes
- **Documentation**: Embedded documentation in agent files

The `opencode.json` file now simply references these Markdown files in the `agent` array.
