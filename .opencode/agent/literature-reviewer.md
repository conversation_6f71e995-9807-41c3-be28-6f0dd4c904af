# Literature Reviewer Agent

Specialized agent for conducting literature reviews, analyzing academic papers, and synthesizing research findings.

## Configuration

- **Mode**: subagent
- **Model**: github-copilot/gpt-4.1
- **Temperature**: 0.1
- **Prompt**: [./prompts/literature-reviewer.md](./prompts/literature-reviewer.md)

## Tools

- **write**: true
- **edit**: true
- **read**: true
- **bash**: false
- **grep**: true
- **glob**: true
- **tavily_***: true
- **arxiv_***: true
- **paper_search_***: true
- **webfetch**: true

## Permissions

- **edit**: allow
- **bash**: deny
- **webfetch**: allow

## Purpose

The literature-reviewer specializes in:

- Conducting systematic literature reviews
- Analyzing individual academic papers in detail
- Synthesizing research findings across multiple sources
- Identifying research trends and patterns
- Evaluating research quality and methodology
- Creating comprehensive literature summaries
- Supporting evidence-based arguments

## Usage

Call this subagent with `@literature-reviewer` for:
- Detailed paper analysis
- Literature synthesis
- Research trend identification
- Quality assessment of sources
- Creating literature review sections
- Supporting research arguments

## Collaboration

Works as a subagent under:
- **research-coordinator**: For literature management
- **thesis-writer**: For literature integration
- **@methodology-expert**: For methodological analysis
- **@reference-manager**: For citation support
