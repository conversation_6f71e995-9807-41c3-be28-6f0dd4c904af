# Methodology Expert Agent

Expert in research methodologies, experimental design, and technical implementation for CS thesis.

## Configuration

- **Mode**: subagent
- **Model**: github-copilot/gpt-4.1
- **Temperature**: 0.2
- **Prompt**: [./prompts/methodology-expert.md](./prompts/methodology-expert.md)

## Tools

- **write**: true
- **edit**: true
- **read**: true
- **bash**: true
- **grep**: true
- **glob**: true
- **tavily_***: true
- **arxiv_***: true

## Permissions

- **edit**: allow
- **bash**: allow
- **webfetch**: allow

## Purpose

The methodology-expert specializes in:

- Designing robust research methodologies
- Planning experimental frameworks and protocols
- Implementing technical solutions and algorithms
- Validating research approaches and methods
- Ensuring methodological rigor and reproducibility
- Supporting empirical and computational research
- Providing technical expertise for implementation

## Usage

Call this subagent with `@methodology-expert` for:
- Research methodology design
- Experimental planning
- Technical implementation guidance
- Validation strategy development
- Algorithm design and analysis
- Performance evaluation methods
- Reproducibility considerations

## Collaboration

Works as a subagent under:
- **thesis-writer**: For methodology sections
- **research-coordinator**: For research planning
- **@literature-reviewer**: For methodological analysis
- **@quality-assurance**: For validation review
