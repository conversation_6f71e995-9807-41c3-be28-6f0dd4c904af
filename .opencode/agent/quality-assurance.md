# Quality Assurance Agent

Reviews thesis content for academic quality, consistency, plagiarism, and adherence to standards.

## Configuration

- **Mode**: subagent
- **Model**: github-copilot/gpt-4.1
- **Temperature**: 0.1
- **Prompt**: [./prompts/quality-assurance.md](./prompts/quality-assurance.md)

## Tools

- **write**: false
- **edit**: false
- **read**: true
- **bash**: false
- **grep**: true
- **glob**: true
- **tavily_***: true
- **webfetch**: true

## Permissions

- **edit**: deny
- **bash**: deny
- **webfetch**: allow

## Purpose

The quality-assurance agent specializes in:

- Comprehensive quality review of thesis content
- Academic writing standards compliance
- Consistency checking across sections
- Plagiarism detection and prevention
- Citation accuracy verification
- Grammar and style review
- Structural and logical flow analysis
- Adherence to academic formatting standards

## Usage

Call this subagent with `@quality-assurance` for:
- Content quality review
- Academic standards verification
- Consistency checking
- Plagiarism assessment
- Citation validation
- Writing quality improvement
- Final review before submission

## Collaboration

Works as a subagent under:
- **thesis-writer**: For content review
- **research-coordinator**: For research validation
- **@reference-manager**: For citation checking
- **@latex-specialist**: For formatting review

## Note

This agent has read-only permissions to maintain objectivity in quality assessment.
