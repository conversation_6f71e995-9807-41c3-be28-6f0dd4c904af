# Reference Manager Agent

Manages citations, bibliography, and reference formatting in LaTeX/BibTeX format.

## Configuration

- **Mode**: subagent
- **Model**: github-copilot/gpt-4.1
- **Temperature**: 0.1
- **Prompt**: [./prompts/reference-manager.md](./prompts/reference-manager.md)

## Tools

- **write**: true
- **edit**: true
- **read**: true
- **bash**: false
- **grep**: true
- **glob**: true
- **arxiv_***: true
- **paper_search_***: true
- **webfetch**: true

## Permissions

- **edit**: allow
- **bash**: deny
- **webfetch**: allow

## Purpose

The reference-manager specializes in:

- Managing BibTeX bibliography files
- Formatting citations according to academic standards
- Ensuring citation accuracy and completeness
- Organizing reference materials
- Maintaining consistent citation styles
- Validating reference information
- Supporting IEEE/ACM citation formats

## Usage

Call this subagent with `@reference-manager` for:
- BibTeX entry creation and management
- Citation formatting and style
- Bibliography organization
- Reference validation
- Citation integration in text
- Reference database maintenance
- Citation style compliance

## Collaboration

Works as a subagent under:
- **thesis-writer**: For citation integration
- **research-coordinator**: For reference organization
- **@literature-reviewer**: For source management
- **@quality-assurance**: For citation validation
