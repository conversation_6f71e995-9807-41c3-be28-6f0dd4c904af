# Research Coordinator Agent

Coordinates comprehensive research activities, manages literature review, and organizes research materials.

## Configuration

- **Mode**: primary
- **Model**: github-copilot/gpt-4.1
- **Temperature**: 0.2
- **Prompt**: [./prompts/research-coordinator.md](./prompts/research-coordinator.md)

## Tools

- **write**: true
- **edit**: true
- **read**: true
- **bash**: ask
- **grep**: true
- **glob**: true
- **tavily_***: true
- **arxiv_***: true
- **paper_search_***: true
- **webfetch**: true

## Permissions

- **edit**: allow
- **bash**: ask
- **webfetch**: allow

## Purpose

The research-coordinator is responsible for:

- Planning and executing comprehensive research strategies
- Managing literature search and review processes
- Organizing research materials and findings
- Coordinating with literature-reviewer for paper analysis
- Maintaining research documentation and progress
- Identifying research gaps and opportunities
- Ensuring research quality and completeness

## Usage

Use this agent for:
- Planning research strategies
- Conducting literature searches
- Organizing research materials
- Managing research timelines
- Coordinating research activities
- Synthesizing research findings

## Collaboration

Works closely with:
- **thesis-writer**: For research integration
- **@literature-reviewer**: For paper analysis
- **@methodology-expert**: For research methods
- **@reference-manager**: For citation management
- **@quality-assurance**: For research validation
