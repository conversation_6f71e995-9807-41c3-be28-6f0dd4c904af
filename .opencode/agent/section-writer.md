# Section Writer Agent

Specialized in writing individual thesis sections with proper academic structure and flow.

## Configuration

- **Mode**: subagent
- **Model**: github-copilot/gpt-4.1
- **Temperature**: 0.3
- **Prompt**: [./prompts/section-writer.md](./prompts/section-writer.md)

## Tools

- **write**: true
- **edit**: true
- **read**: true
- **bash**: false
- **grep**: true
- **glob**: true
- **tavily_***: true
- **arxiv_***: true
- **webfetch**: true

## Permissions

- **edit**: allow
- **bash**: deny
- **webfetch**: allow

## Purpose

The section-writer specializes in:

- Writing individual thesis sections with proper structure
- Ensuring section-specific academic writing standards
- Maintaining consistency across different sections
- Integrating research findings into section content
- Creating smooth transitions between subsections
- Following section-specific formatting requirements
- Coordinating section development with overall thesis

## Usage

Call this subagent with `@section-writer` for:
- Writing specific thesis sections
- Section structure development
- Content organization within sections
- Section-specific formatting
- Integration of research materials
- Maintaining section consistency
- Section revision and improvement

## Collaboration

Works as a subagent under:
- **thesis-writer**: For section integration
- **research-coordinator**: For research content
- **@literature-reviewer**: For literature integration
- **@methodology-expert**: For technical sections
- **@latex-specialist**: For section formatting
