# Thesis Writer Agent

Primary agent for writing thesis content with comprehensive LaTeX and academic writing expertise.

## Configuration

- **Mode**: primary
- **Model**: github-copilot/gpt-4.1
- **Temperature**: 0.3
- **Prompt**: [./prompts/thesis-writer.md](./prompts/thesis-writer.md)

## Tools

- **write**: true
- **edit**: true
- **read**: true
- **bash**: true
- **grep**: true
- **glob**: true
- **tavily_***: true
- **arxiv_***: true
- **paper_search_***: true

## Permissions

- **edit**: allow
- **bash**: ask
- **webfetch**: allow

## Purpose

The thesis-writer is the primary agent responsible for:

- Writing high-quality academic content for all thesis sections
- Ensuring proper LaTeX formatting and document structure
- Integrating research findings into coherent academic prose
- Maintaining consistency in writing style and terminology
- Coordinating with other agents for comprehensive thesis development
- Managing the overall thesis writing process and timeline

## Usage

This agent should be used as your main interface for:
- Writing and editing thesis content
- Integrating research materials into sections
- Ensuring academic writing standards
- Managing the overall thesis structure
- Coordinating with specialized subagents

## Collaboration

Works closely with:
- **research-coordinator**: For research integration
- **@literature-reviewer**: For literature synthesis
- **@methodology-expert**: For technical content
- **@quality-assurance**: For content review
- **@latex-specialist**: For formatting
- **@section-writer**: For individual sections
- **@reference-manager**: For citations
