---
description: Conduct comprehensive literature search on a specific topic
agent: research-coordinator
model: github-copilot/gpt-4.1
subtask: true
---

Conduct a comprehensive literature search on: $ARGUMENTS

Please perform the following systematic literature search:

1. **Initial Search Strategy**:
   - Use multiple academic databases (ArXiv, IEEE Xplore, ACM Digital Library, Google Scholar)
   - Apply relevant keywords and Boolean search operators
   - Include both recent papers (last 5 years) and seminal works
   - Search for review papers and surveys in the area

2. **Search Execution**:
   - Document search terms and strategies used
   - Record number of results from each database
   - Apply inclusion/exclusion criteria systematically
   - Perform forward and backward citation analysis

3. **Paper Selection and Analysis**:
   - Prioritize highly cited and recent papers
   - Include papers from top-tier venues and journals
   - Ensure diverse methodological approaches
   - Select papers that directly relate to thesis objectives

4. **Documentation and Organization**:
   - Create detailed summaries for each selected paper
   - Organize papers by themes and relevance
   - Identify key authors and research groups
   - Note research gaps and opportunities

5. **Output Generation**:
   - Save findings to `thesis/.context/research/literature-search/`
   - Create bibliography entries for all relevant papers
   - Generate research summary with key insights
   - Provide recommendations for further investigation

Focus on finding high-quality, peer-reviewed sources that will strengthen the thesis literature review and provide solid foundation for the research.
