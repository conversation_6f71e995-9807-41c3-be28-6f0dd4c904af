---
description: Create or update comprehensive thesis outline
agent: thesis-writer
model: github-copilot/gpt-4.1
---

Create a comprehensive thesis outline for: $ARGUMENTS

Please develop a detailed thesis outline with the following structure:

1. **Thesis Overview**:
   - Title and research focus
   - Main research questions and objectives
   - Key contributions and expected outcomes
   - Scope and limitations

2. **Chapter Structure** (following IMRaD format):
   
   **Chapter 1: Introduction**
   - Problem statement and motivation
   - Research questions and hypotheses
   - Thesis contributions and significance
   - Thesis organization and structure
   
   **Chapter 2: Literature Review**
   - Theoretical foundations and background
   - Related work and existing approaches
   - Research gaps and opportunities
   - Positioning of current research
   
   **Chapter 3: Methodology**
   - Research design and approach
   - Data collection and analysis methods
   - Implementation details and tools
   - Validation and evaluation framework
   
   **Chapter 4: Implementation/Results**
   - System design and architecture
   - Implementation details and challenges
   - Experimental setup and procedures
   - Results presentation and analysis
   
   **Chapter 5: Discussion**
   - Interpretation of results
   - Comparison with existing work
   - Implications and significance
   - Limitations and threats to validity
   
   **Chapter 6: Conclusion**
   - Summary of contributions
   - Achievement of objectives
   - Future work and recommendations
   - Final reflections

3. **Supporting Elements**:
   - Abstract and acknowledgments
   - List of figures, tables, and algorithms
   - Bibliography and references
   - Appendices (if needed)

4. **Timeline and Milestones**:
   - Estimated completion dates for each chapter
   - Key milestones and review points
   - Dependencies between sections
   - Buffer time for revisions

Save the outline to `thesis/.context/outline.md` and create individual section files in `thesis/sections/` with basic structure templates.
