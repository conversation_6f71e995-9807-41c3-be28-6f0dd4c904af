{"$schema": "https://opencode.ai/config.json", "instructions": ["AGENTS.md", "thesis/.context/guidelines/*.md"], "agent": ["./agent/thesis-writer.md", "./agent/research-coordinator.md", "./agent/literature-reviewer.md", "./agent/methodology-expert.md", "./agent/quality-assurance.md", "./agent/reference-manager.md", "./agent/latex-specialist.md", "./agent/section-writer.md"], "mcp": {"tavily-search": {"type": "remote", "url": "https://api.tavily.com/mcp", "enabled": true, "headers": {"Authorization": "Bearer YOUR_TAVILY_API_KEY"}}, "arxiv-papers": {"type": "local", "command": ["npx", "@blazickjp/arxiv-mcp-server"], "enabled": true}, "paper-search": {"type": "local", "command": ["npx", "@openags/paper-search-mcp"], "enabled": true}}, "command": {"research": {"description": "Start comprehensive research on a topic", "template": "I need to conduct comprehensive research on: $ARGUMENTS\n\nPlease:\n1. Search for relevant academic papers and sources\n2. Analyze current state of research\n3. Identify key papers and authors\n4. Create a research summary\n5. Save findings to thesis/.context/research/\n\nFocus on recent publications (last 5 years) and highly cited works.", "agent": "research-coordinator", "subtask": true}, "write-section": {"description": "Write a specific thesis section", "template": "Write the following thesis section: $ARGUMENTS\n\nRequirements:\n- Follow academic writing standards\n- Use proper LaTeX formatting\n- Include relevant citations\n- Maintain consistency with existing sections\n- Save to appropriate file in thesis/sections/\n\nRefer to thesis/.context/outline.md for structure and thesis/.context/research/ for sources.", "agent": "section-writer", "subtask": true}, "review-quality": {"description": "Comprehensive quality review of thesis content", "template": "Perform comprehensive quality review of: $ARGUMENTS\n\nCheck for:\n- Academic writing quality and clarity\n- Logical flow and structure\n- Citation accuracy and completeness\n- Consistency across sections\n- Potential plagiarism issues\n- Grammar and style\n- LaTeX formatting\n\nProvide detailed feedback and suggestions for improvement.", "agent": "quality-assurance", "subtask": true}}}