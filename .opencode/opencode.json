{"$schema": "https://opencode.ai/config.json", "instructions": ["AGENTS.md", "thesis/.context/guidelines/*.md"], "agent": {"thesis-writer": {"description": "Primary agent for writing thesis content with comprehensive LaTeX and academic writing expertise", "mode": "primary", "model": "github-copilot/gpt-4.1", "temperature": 0.3, "prompt": "{file:./prompts/thesis-writer.md}", "tools": {"write": true, "edit": true, "read": true, "bash": true, "grep": true, "glob": true, "tavily_*": true, "arxiv_*": true, "paper_search_*": true}, "permission": {"edit": "allow", "bash": "ask", "webfetch": "allow"}}, "research-coordinator": {"description": "Coordinates comprehensive research activities, manages literature review, and organizes research materials", "mode": "primary", "model": "github-copilot/gpt-4.1", "temperature": 0.2, "prompt": "{file:./prompts/research-coordinator.md}", "tools": {"write": true, "edit": true, "read": true, "bash": "ask", "grep": true, "glob": true, "tavily_*": true, "arxiv_*": true, "paper_search_*": true, "webfetch": true}, "permission": {"edit": "allow", "bash": "ask", "webfetch": "allow"}}, "literature-reviewer": {"description": "Specialized agent for conducting literature reviews, analyzing academic papers, and synthesizing research findings", "mode": "subagent", "model": "github-copilot/gpt-4.1", "temperature": 0.1, "prompt": "{file:./prompts/literature-reviewer.md}", "tools": {"write": true, "edit": true, "read": true, "bash": false, "grep": true, "glob": true, "tavily_*": true, "arxiv_*": true, "paper_search_*": true, "webfetch": true}, "permission": {"edit": "allow", "bash": "deny", "webfetch": "allow"}}, "methodology-expert": {"description": "Expert in research methodologies, experimental design, and technical implementation for CS thesis", "mode": "subagent", "model": "github-copilot/gpt-4.1", "temperature": 0.2, "prompt": "{file:./prompts/methodology-expert.md}", "tools": {"write": true, "edit": true, "read": true, "bash": true, "grep": true, "glob": true, "tavily_*": true, "arxiv_*": true}, "permission": {"edit": "allow", "bash": "allow", "webfetch": "allow"}}, "quality-assurance": {"description": "Reviews thesis content for academic quality, consistency, plagiarism, and adherence to standards", "mode": "subagent", "model": "github-copilot/gpt-4.1", "temperature": 0.1, "prompt": "{file:./prompts/quality-assurance.md}", "tools": {"write": false, "edit": false, "read": true, "bash": false, "grep": true, "glob": true, "tavily_*": true, "webfetch": true}, "permission": {"edit": "deny", "bash": "deny", "webfetch": "allow"}}, "reference-manager": {"description": "Manages citations, bibliography, and reference formatting in LaTeX/BibTeX format", "mode": "subagent", "model": "github-copilot/gpt-4.1", "temperature": 0.1, "prompt": "{file:./prompts/reference-manager.md}", "tools": {"write": true, "edit": true, "read": true, "bash": false, "grep": true, "glob": true, "arxiv_*": true, "paper_search_*": true, "webfetch": true}, "permission": {"edit": "allow", "bash": "deny", "webfetch": "allow"}}, "latex-specialist": {"description": "Expert in LaTeX formatting, document structure, and academic document preparation", "mode": "subagent", "model": "github-copilot/gpt-4.1", "temperature": 0.1, "prompt": "{file:./prompts/latex-specialist.md}", "tools": {"write": true, "edit": true, "read": true, "bash": true, "grep": true, "glob": true}, "permission": {"edit": "allow", "bash": "allow", "webfetch": "deny"}}, "section-writer": {"description": "Specialized in writing individual thesis sections with proper academic structure and flow", "mode": "subagent", "model": "github-copilot/gpt-4.1", "temperature": 0.3, "prompt": "{file:./prompts/section-writer.md}", "tools": {"write": true, "edit": true, "read": true, "bash": false, "grep": true, "glob": true, "tavily_*": true, "arxiv_*": true, "webfetch": true}, "permission": {"edit": "allow", "bash": "deny", "webfetch": "allow"}}}, "mcp": {"tavily-search": {"type": "remote", "url": "https://api.tavily.com/mcp", "enabled": true, "headers": {"Authorization": "Bearer YOUR_TAVILY_API_KEY"}}, "arxiv-papers": {"type": "local", "command": ["npx", "@blazickjp/arxiv-mcp-server"], "enabled": true}, "paper-search": {"type": "local", "command": ["npx", "@openags/paper-search-mcp"], "enabled": true}}, "command": {"research": {"description": "Start comprehensive research on a topic", "template": "I need to conduct comprehensive research on: $ARGUMENTS\n\nPlease:\n1. Search for relevant academic papers and sources\n2. Analyze current state of research\n3. Identify key papers and authors\n4. Create a research summary\n5. Save findings to thesis/.context/research/\n\nFocus on recent publications (last 5 years) and highly cited works.", "agent": "research-coordinator", "subtask": true}, "write-section": {"description": "Write a specific thesis section", "template": "Write the following thesis section: $ARGUMENTS\n\nRequirements:\n- Follow academic writing standards\n- Use proper LaTeX formatting\n- Include relevant citations\n- Maintain consistency with existing sections\n- Save to appropriate file in thesis/sections/\n\nRefer to thesis/.context/outline.md for structure and thesis/.context/research/ for sources.", "agent": "section-writer", "subtask": true}, "review-quality": {"description": "Comprehensive quality review of thesis content", "template": "Perform comprehensive quality review of: $ARGUMENTS\n\nCheck for:\n- Academic writing quality and clarity\n- Logical flow and structure\n- Citation accuracy and completeness\n- Consistency across sections\n- Potential plagiarism issues\n- Grammar and style\n- LaTeX formatting\n\nProvide detailed feedback and suggestions for improvement.", "agent": "quality-assurance", "subtask": true}}}