import { tool } from "@opencode-ai/plugin"
import * as fs from 'fs'
import * as path from 'path'

export default tool({
  description: "Generate and manage BibTeX citations for academic papers and sources",
  args: {
    action: tool.schema.enum(["generate", "validate", "search", "format"]).describe("Action: generate new citation, validate existing, search bibliography, or format citation"),
    title: tool.schema.string().optional().describe("Paper/source title"),
    authors: tool.schema.string().optional().describe("Authors (comma-separated)"),
    year: tool.schema.number().optional().describe("Publication year"),
    venue: tool.schema.string().optional().describe("Journal/conference name"),
    doi: tool.schema.string().optional().describe("DOI identifier"),
    url: tool.schema.string().optional().describe("URL for web sources"),
    type: tool.schema.enum(["article", "inproceedings", "book", "incollection", "misc", "techreport"]).optional().describe("Publication type"),
    key: tool.schema.string().optional().describe("Citation key for search/validation"),
    pages: tool.schema.string().optional().describe("Page numbers"),
    volume: tool.schema.string().optional().describe("Volume number"),
    number: tool.schema.string().optional().describe("Issue number")
  },
  async execute(args, context) {
    const bibFile = path.join(process.cwd(), 'thesis', 'references', 'bibliography.bib')
    
    switch (args.action) {
      case 'generate':
        return generateBibTeXEntry(args)
        
      case 'validate':
        return validateBibliography(bibFile)
        
      case 'search':
        return searchBibliography(bibFile, args.key || args.title || '')
        
      case 'format':
        return formatCitation(args)
        
      default:
        return "Error: Invalid action"
    }
  }
})

function generateBibTeXEntry(args: any): string {
  if (!args.title || !args.authors || !args.year) {
    return "Error: Title, authors, and year are required for citation generation"
  }
  
  // Generate citation key
  const firstAuthor = args.authors.split(',')[0].trim().split(' ').pop() // Last name
  const key = `${firstAuthor.toLowerCase()}${args.year}${args.title.split(' ')[0].toLowerCase()}`
  
  // Determine entry type
  const entryType = args.type || (args.venue?.toLowerCase().includes('conference') || args.venue?.toLowerCase().includes('proceedings') ? 'inproceedings' : 'article')
  
  let bibEntry = `@${entryType}{${key},\n`
  bibEntry += `  author = {${args.authors}},\n`
  bibEntry += `  title = {${args.title}},\n`
  
  switch (entryType) {
    case 'article':
      bibEntry += `  journal = {${args.venue || 'Journal Name'}},\n`
      if (args.volume) bibEntry += `  volume = {${args.volume}},\n`
      if (args.number) bibEntry += `  number = {${args.number}},\n`
      break
      
    case 'inproceedings':
      bibEntry += `  booktitle = {${args.venue || 'Conference Proceedings'}},\n`
      break
      
    case 'book':
      bibEntry += `  publisher = {${args.venue || 'Publisher'}},\n`
      break
      
    case 'misc':
      if (args.url) bibEntry += `  howpublished = {\\url{${args.url}}},\n`
      break
  }
  
  if (args.pages) bibEntry += `  pages = {${args.pages}},\n`
  bibEntry += `  year = {${args.year}},\n`
  if (args.doi) bibEntry += `  doi = {${args.doi}},\n`
  if (args.url && entryType !== 'misc') bibEntry += `  url = {${args.url}},\n`
  
  bibEntry += `}\n`
  
  return `Generated BibTeX entry:\n\n${bibEntry}\n\nCitation key: ${key}\nIn-text citation: [${key}] or \\cite{${key}}`
}

function validateBibliography(bibFile: string): string {
  if (!fs.existsSync(bibFile)) {
    return "Error: Bibliography file not found at thesis/references/bibliography.bib"
  }
  
  const content = fs.readFileSync(bibFile, 'utf8')
  const issues: string[] = []
  const entries: string[] = []
  
  // Extract all BibTeX entries
  const entryRegex = /@(\w+)\{([^,]+),/g
  let match
  while ((match = entryRegex.exec(content)) !== null) {
    entries.push(match[2])
  }
  
  // Check for duplicate keys
  const duplicates = entries.filter((item, index) => entries.indexOf(item) !== index)
  if (duplicates.length > 0) {
    issues.push(`Duplicate citation keys found: ${duplicates.join(', ')}`)
  }
  
  // Check for common formatting issues
  const lines = content.split('\n')
  lines.forEach((line, index) => {
    const lineNum = index + 1
    
    // Check for missing commas
    if (line.includes('=') && !line.includes(',') && !line.includes('}') && !line.trim().endsWith(',')) {
      issues.push(`Line ${lineNum}: Missing comma after field`)
    }
    
    // Check for unmatched braces
    const openBraces = (line.match(/\{/g) || []).length
    const closeBraces = (line.match(/\}/g) || []).length
    if (openBraces !== closeBraces && line.includes('=')) {
      issues.push(`Line ${lineNum}: Unmatched braces`)
    }
  })
  
  // Check for required fields
  const requiredFields = {
    'article': ['author', 'title', 'journal', 'year'],
    'inproceedings': ['author', 'title', 'booktitle', 'year'],
    'book': ['author', 'title', 'publisher', 'year']
  }
  
  const entryBlocks = content.split('@').filter(block => block.trim().length > 0)
  entryBlocks.forEach(block => {
    const typeMatch = block.match(/^(\w+)\{/)
    if (typeMatch) {
      const type = typeMatch[1].toLowerCase()
      const required = requiredFields[type as keyof typeof requiredFields]
      if (required) {
        required.forEach(field => {
          if (!block.includes(`${field} =`)) {
            const keyMatch = block.match(/\{([^,]+),/)
            const key = keyMatch ? keyMatch[1] : 'unknown'
            issues.push(`Entry ${key}: Missing required field '${field}' for ${type}`)
          }
        })
      }
    }
  })
  
  if (issues.length === 0) {
    return `✅ Bibliography validation passed!\n\nFound ${entries.length} entries with no issues detected.`
  } else {
    return `❌ Bibliography validation found ${issues.length} issues:\n\n${issues.map((issue, i) => `${i + 1}. ${issue}`).join('\n')}\n\nPlease fix these issues for proper LaTeX compilation.`
  }
}

function searchBibliography(bibFile: string, query: string): string {
  if (!fs.existsSync(bibFile)) {
    return "Error: Bibliography file not found"
  }
  
  if (!query) {
    return "Error: Search query required"
  }
  
  const content = fs.readFileSync(bibFile, 'utf8')
  const results: string[] = []
  
  // Split into entries
  const entries = content.split('@').filter(entry => entry.trim().length > 0)
  
  entries.forEach(entry => {
    const lowerEntry = entry.toLowerCase()
    const lowerQuery = query.toLowerCase()
    
    if (lowerEntry.includes(lowerQuery)) {
      // Extract key and title
      const keyMatch = entry.match(/\{([^,]+),/)
      const titleMatch = entry.match(/title\s*=\s*\{([^}]+)\}/)
      const authorMatch = entry.match(/author\s*=\s*\{([^}]+)\}/)
      
      if (keyMatch) {
        const key = keyMatch[1]
        const title = titleMatch ? titleMatch[1] : 'No title'
        const author = authorMatch ? authorMatch[1] : 'No author'
        results.push(`Key: ${key}\nTitle: ${title}\nAuthor: ${author}\n`)
      }
    }
  })
  
  if (results.length === 0) {
    return `No entries found matching "${query}"`
  }
  
  return `Found ${results.length} matching entries:\n\n${results.join('\n---\n')}`
}

function formatCitation(args: any): string {
  if (!args.key && !args.title) {
    return "Error: Citation key or title required for formatting"
  }
  
  const key = args.key || generateKeyFromTitle(args.title || '', args.year || new Date().getFullYear())
  
  return `Citation Formats:

**IEEE Style (Numbered):**
In-text: [${Math.floor(Math.random() * 50) + 1}]
Reference: [${Math.floor(Math.random() * 50) + 1}] ${args.authors || 'Author'}, "${args.title || 'Title'}," ${args.venue || 'Venue'}, ${args.year || 'Year'}.

**LaTeX Commands:**
\\cite{${key}} - Basic citation
\\citep{${key}} - Parenthetical citation  
\\citet{${key}} - Textual citation
\\cite[p. 123]{${key}} - Citation with page number

**BibTeX Key:** ${key}

**Usage Examples:**
- "Recent research \\cite{${key}} has shown..."
- "As demonstrated by Smith et al. \\cite{${key}}, the approach..."
- "This method (\\cite{${key}}) provides better results..."

**Cross-references:**
- Figure: \\cref{fig:example}
- Table: \\cref{tab:results}  
- Equation: \\cref{eq:formula}
- Section: \\cref{sec:methodology}`
}

function generateKeyFromTitle(title: string, year: number): string {
  const words = title.toLowerCase().split(' ').filter(word => word.length > 3)
  const keyWord = words[0] || 'paper'
  return `author${year}${keyWord}`
}
