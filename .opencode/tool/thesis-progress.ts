import { tool } from "@opencode-ai/plugin"
import * as fs from 'fs'
import * as path from 'path'

export default tool({
  description: "Track and report thesis writing progress across all sections",
  args: {
    action: tool.schema.enum(["status", "update", "summary"]).describe("Action to perform: status (show current progress), update (update progress), or summary (detailed report)"),
    section: tool.schema.string().optional().describe("Specific section to update (e.g., 'introduction', 'literature-review')"),
    progress: tool.schema.number().min(0).max(100).optional().describe("Progress percentage for the section (0-100)"),
    notes: tool.schema.string().optional().describe("Progress notes or comments")
  },
  async execute(args, context) {
    const thesisDir = path.join(process.cwd(), 'thesis')
    const progressFile = path.join(thesisDir, '.context', 'progress.json')
    
    // Initialize progress data structure
    const defaultProgress = {
      lastUpdated: new Date().toISOString(),
      overallProgress: 0,
      sections: {
        'abstract': { progress: 0, notes: '', lastUpdated: '' },
        'introduction': { progress: 0, notes: '', lastUpdated: '' },
        'literature-review': { progress: 0, notes: '', lastUpdated: '' },
        'methodology': { progress: 0, notes: '', lastUpdated: '' },
        'implementation': { progress: 0, notes: '', lastUpdated: '' },
        'results': { progress: 0, notes: '', lastUpdated: '' },
        'discussion': { progress: 0, notes: '', lastUpdated: '' },
        'conclusion': { progress: 0, notes: '', lastUpdated: '' }
      },
      milestones: {
        'research-complete': false,
        'first-draft-complete': false,
        'review-complete': false,
        'final-draft-complete': false
      }
    }
    
    // Load existing progress or create new
    let progressData = defaultProgress
    if (fs.existsSync(progressFile)) {
      try {
        progressData = JSON.parse(fs.readFileSync(progressFile, 'utf8'))
      } catch (error) {
        console.warn('Could not read progress file, using defaults')
      }
    }
    
    switch (args.action) {
      case 'status':
        return formatProgressStatus(progressData)
        
      case 'update':
        if (!args.section || args.progress === undefined) {
          return "Error: Section and progress percentage required for update action"
        }
        
        if (!progressData.sections[args.section]) {
          return `Error: Unknown section '${args.section}'. Valid sections: ${Object.keys(progressData.sections).join(', ')}`
        }
        
        // Update section progress
        progressData.sections[args.section] = {
          progress: args.progress,
          notes: args.notes || progressData.sections[args.section].notes,
          lastUpdated: new Date().toISOString()
        }
        
        // Calculate overall progress
        const totalProgress = Object.values(progressData.sections).reduce((sum, section) => sum + section.progress, 0)
        progressData.overallProgress = Math.round(totalProgress / Object.keys(progressData.sections).length)
        progressData.lastUpdated = new Date().toISOString()
        
        // Update milestones
        updateMilestones(progressData)
        
        // Save progress
        fs.writeFileSync(progressFile, JSON.stringify(progressData, null, 2))
        
        return `Updated ${args.section} progress to ${args.progress}%. Overall progress: ${progressData.overallProgress}%`
        
      case 'summary':
        return formatDetailedSummary(progressData)
        
      default:
        return "Error: Invalid action. Use 'status', 'update', or 'summary'"
    }
  }
})

function formatProgressStatus(data: any): string {
  const sections = Object.entries(data.sections)
    .map(([name, info]: [string, any]) => `  ${name}: ${info.progress}%`)
    .join('\n')
  
  const milestones = Object.entries(data.milestones)
    .map(([name, completed]: [string, any]) => `  ${name}: ${completed ? '✓' : '○'}`)
    .join('\n')
  
  return `# Thesis Progress Status

## Overall Progress: ${data.overallProgress}%
Last Updated: ${new Date(data.lastUpdated).toLocaleDateString()}

## Section Progress:
${sections}

## Milestones:
${milestones}

## Next Steps:
${getNextSteps(data)}`
}

function formatDetailedSummary(data: any): string {
  const sectionDetails = Object.entries(data.sections)
    .map(([name, info]: [string, any]) => {
      const lastUpdated = info.lastUpdated ? new Date(info.lastUpdated).toLocaleDateString() : 'Never'
      return `### ${name.replace('-', ' ').toUpperCase()}
- Progress: ${info.progress}%
- Last Updated: ${lastUpdated}
- Notes: ${info.notes || 'No notes'}
`
    }).join('\n')
  
  return `# Detailed Thesis Progress Report

## Overall Statistics
- **Total Progress**: ${data.overallProgress}%
- **Last Updated**: ${new Date(data.lastUpdated).toLocaleDateString()}
- **Sections Completed**: ${Object.values(data.sections).filter((s: any) => s.progress >= 100).length}/8
- **Sections In Progress**: ${Object.values(data.sections).filter((s: any) => s.progress > 0 && s.progress < 100).length}/8

## Section Details
${sectionDetails}

## Milestone Status
${Object.entries(data.milestones).map(([name, completed]: [string, any]) => 
  `- ${name.replace('-', ' ')}: ${completed ? '✅ Complete' : '⏳ Pending'}`
).join('\n')}

## Recommendations
${getRecommendations(data)}`
}

function updateMilestones(data: any) {
  const sections = data.sections
  
  // Research complete: literature review at least 80%
  data.milestones['research-complete'] = sections['literature-review'].progress >= 80
  
  // First draft complete: all sections at least 70%
  data.milestones['first-draft-complete'] = Object.values(sections).every((s: any) => s.progress >= 70)
  
  // Review complete: all sections at least 90%
  data.milestones['review-complete'] = Object.values(sections).every((s: any) => s.progress >= 90)
  
  // Final draft complete: all sections 100%
  data.milestones['final-draft-complete'] = Object.values(sections).every((s: any) => s.progress >= 100)
}

function getNextSteps(data: any): string {
  const incompleteSections = Object.entries(data.sections)
    .filter(([_, info]: [string, any]) => info.progress < 100)
    .sort(([_, a]: [string, any], [__, b]: [string, any]) => a.progress - b.progress)
  
  if (incompleteSections.length === 0) {
    return "🎉 All sections complete! Ready for final review and submission."
  }
  
  const [nextSection, info] = incompleteSections[0] as [string, any]
  return `Focus on: ${nextSection.replace('-', ' ')} (${info.progress}% complete)`
}

function getRecommendations(data: any): string {
  const recommendations = []
  
  // Check for sections that haven't been updated recently
  const staleThreshold = 7 * 24 * 60 * 60 * 1000 // 7 days
  const staleSections = Object.entries(data.sections)
    .filter(([_, info]: [string, any]) => {
      if (!info.lastUpdated) return true
      return Date.now() - new Date(info.lastUpdated).getTime() > staleThreshold
    })
  
  if (staleSections.length > 0) {
    recommendations.push(`📅 Update progress for stale sections: ${staleSections.map(([name]) => name).join(', ')}`)
  }
  
  // Check for uneven progress
  const progressValues = Object.values(data.sections).map((s: any) => s.progress)
  const maxProgress = Math.max(...progressValues)
  const minProgress = Math.min(...progressValues)
  
  if (maxProgress - minProgress > 50) {
    recommendations.push("⚖️ Consider balancing work across sections - some are significantly ahead of others")
  }
  
  // Milestone-based recommendations
  if (!data.milestones['research-complete']) {
    recommendations.push("📚 Focus on completing literature review to establish research foundation")
  }
  
  if (data.overallProgress > 50 && !data.milestones['first-draft-complete']) {
    recommendations.push("✍️ Work towards completing first drafts of all sections")
  }
  
  if (recommendations.length === 0) {
    recommendations.push("✅ Progress looks good! Keep up the steady work.")
  }
  
  return recommendations.join('\n')
}
