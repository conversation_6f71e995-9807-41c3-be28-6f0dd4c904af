{"python.pythonPath": "./backend/.venv/bin/python", "python.defaultInterpreterPath": "./backend/.venv/bin/python", "python.terminal.activateEnvironment": true, "python.terminal.activateEnvInCurrentTerminal": true, "python.analysis.extraPaths": ["./backend"], "python.analysis.autoSearchPaths": true, "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "standard", "python.analysis.diagnosticMode": "workspace", "python.analysis.include": ["./backend/**"], "python.analysis.exclude": ["./backend/.venv/**", "./backend/__pycache__/**", "./backend/.pytest_cache/**"], "python.testing.pytestEnabled": true, "python.testing.pytestArgs": ["./backend"], "python.testing.unittestEnabled": false, "python.testing.cwd": "./backend"}