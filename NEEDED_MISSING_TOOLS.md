# Missing Tools and MCP Servers for Thesis Writing

This document lists the tools and MCP servers that need to be installed and configured to fully utilize the OpenCode thesis writing system.

## Required MCP Servers

### 1. <PERSON>ly Web Search MCP Server
**Purpose**: Real-time web search for research and fact-checking
**Status**: ❌ Not installed
**Installation**: 
```bash
# This is a remote MCP server - requires API key
# Sign up at https://tavily.com/ to get API key
# Update .opencode/opencode.json with your API key
```
**Configuration**: Update the `YOUR_TAVILY_API_KEY` placeholder in `.opencode/opencode.json`

### 2. ArXiv Papers MCP Server
**Purpose**: Search and retrieve academic papers from ArXiv
**Status**: ❌ Not installed
**Installation**:
```bash
npm install -g @blazickjp/arxiv-mcp-server
# or
npx @blazickjp/arxiv-mcp-server
```
**GitHub**: https://github.com/blazickjp/arxiv-mcp-server

### 3. Paper Search MCP Server
**Purpose**: Multi-source academic paper search (ArXiv, PubMed, bioRxiv, Sci-Hub)
**Status**: ❌ Not installed
**Installation**:
```bash
npm install -g @openags/paper-search-mcp
# or
npx @openags/paper-search-mcp
```
**GitHub**: https://github.com/openags/paper-search-mcp

## Recommended Additional MCP Servers

### 4. Google Scholar MCP Server
**Purpose**: Search Google Scholar for academic papers and citations
**Status**: ❌ Not available (needs to be found or created)
**Alternative**: Use Tavily with "site:scholar.google.com" queries

### 5. Semantic Scholar MCP Server
**Purpose**: Access Semantic Scholar API for paper metadata and citations
**Status**: ❌ Not available (needs to be found or created)
**API**: https://www.semanticscholar.org/product/api

### 6. Zotero MCP Server
**Purpose**: Integration with Zotero reference manager
**Status**: ❌ Not available (needs to be found or created)
**Alternative**: Manual BibTeX management with citation-helper tool

### 7. Grammarly/LanguageTool MCP Server
**Purpose**: Grammar and style checking for academic writing
**Status**: ❌ Not available (needs to be found or created)
**Alternative**: Manual proofreading and external tools

### 8. Plagiarism Detection MCP Server
**Purpose**: Check for plagiarism and similarity
**Status**: ❌ Not available (needs to be found or created)
**Alternative**: Manual checking with online tools

## Custom Tools to Develop

### 9. LaTeX Compilation Tool
**Purpose**: Automated LaTeX compilation with error reporting
**Status**: ⚠️ Partially implemented in commands
**Needed**: Enhanced error parsing and resolution suggestions

### 10. Reference Validation Tool
**Purpose**: Validate and format references according to academic standards
**Status**: ⚠️ Basic validation in citation-helper tool
**Needed**: Enhanced validation rules and formatting options

### 11. Word Count and Statistics Tool
**Purpose**: Track word counts, reading time, and writing statistics
**Status**: ❌ Not implemented
**Needed**: Section-wise word counts, progress tracking

### 12. Figure and Table Management Tool
**Purpose**: Manage figures, tables, and their references
**Status**: ❌ Not implemented
**Needed**: Automatic numbering, reference checking

## External Tools and Services

### Academic Databases Access
- **IEEE Xplore**: Requires institutional access
- **ACM Digital Library**: Requires institutional access
- **SpringerLink**: Requires institutional access
- **ScienceDirect**: Requires institutional access

### Reference Management
- **Zotero**: Free, recommended for bibliography management
- **Mendeley**: Free alternative
- **EndNote**: Commercial, institutional licenses available

### Writing and Editing Tools
- **Grammarly**: Grammar and style checking
- **Hemingway Editor**: Readability improvement
- **ProWritingAid**: Comprehensive writing analysis

### LaTeX Tools
- **TeXLive**: Full LaTeX distribution
- **Overleaf**: Online LaTeX editor (alternative to local setup)
- **TeXstudio**: LaTeX IDE with advanced features

### Plagiarism Detection
- **Turnitin**: Institutional access required
- **Copyscape**: Commercial plagiarism detection
- **Quetext**: Free plagiarism checker with limitations

## Setup Instructions

### 1. Install Node.js and npm
```bash
# Install Node.js (required for MCP servers)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version
npm --version
```

### 2. Install OpenCode
```bash
# Install OpenCode CLI
npm install -g @opencode-ai/cli

# Verify installation
opencode --version
```

### 3. Configure API Keys
Create a `.env` file in the project root with your API keys:
```bash
# .env file
TAVILY_API_KEY=your_tavily_api_key_here
OPENAI_API_KEY=your_openai_key_here  # if using OpenAI models
ANTHROPIC_API_KEY=your_anthropic_key_here  # if using Claude models
```

### 4. Install LaTeX Distribution
```bash
# Ubuntu/Debian
sudo apt-get install texlive-full

# macOS
brew install --cask mactex

# Windows
# Download and install MiKTeX or TeX Live from official websites
```

### 5. Install MCP Servers
```bash
# Install available MCP servers
npm install -g @blazickjp/arxiv-mcp-server
npm install -g @openags/paper-search-mcp

# Test installations
npx @blazickjp/arxiv-mcp-server --help
npx @openags/paper-search-mcp --help
```

### 6. Configure OpenCode
```bash
# Navigate to project directory
cd /path/to/your/thesis/project

# Initialize OpenCode (if not already done)
opencode init

# Test configuration
opencode agent list
opencode command list
```

## Priority Installation Order

### High Priority (Essential)
1. ✅ OpenCode CLI and configuration
2. ❌ Tavily API key and configuration
3. ❌ ArXiv MCP server
4. ❌ LaTeX distribution (TeXLive or MiKTeX)

### Medium Priority (Recommended)
5. ❌ Paper Search MCP server
6. ❌ Zotero or Mendeley for reference management
7. ❌ Grammarly or similar writing assistant

### Low Priority (Nice to Have)
8. ❌ Additional academic database access
9. ❌ Advanced LaTeX IDE (TeXstudio)
10. ❌ Plagiarism detection service

## Troubleshooting Common Issues

### MCP Server Connection Issues
- Verify Node.js and npm are properly installed
- Check that MCP servers are globally installed
- Ensure OpenCode configuration points to correct server paths
- Check firewall and network connectivity

### LaTeX Compilation Issues
- Verify LaTeX distribution is complete
- Check that all required packages are installed
- Ensure proper file paths and permissions
- Review LaTeX error logs for specific issues

### API Key Issues
- Verify API keys are valid and active
- Check rate limits and usage quotas
- Ensure proper environment variable configuration
- Test API access independently

### Performance Issues
- Monitor system resources during compilation
- Consider using faster SSD storage
- Optimize LaTeX compilation with partial builds
- Use efficient MCP server configurations

## Getting Help

### OpenCode Support
- Documentation: https://opencode.ai/docs/
- GitHub Issues: https://github.com/opencode-ai/opencode
- Discord Community: [Link to Discord]

### Academic Writing Resources
- University writing centers
- Academic writing guides and handbooks
- LaTeX documentation and communities
- Research methodology resources

### Technical Support
- LaTeX Stack Exchange for LaTeX issues
- Stack Overflow for programming questions
- GitHub issues for specific tool problems
- Academic IT support for institutional access

## Next Steps

1. **Immediate**: Install and configure high-priority tools
2. **Week 1**: Set up basic MCP servers and test functionality
3. **Week 2**: Configure reference management and writing tools
4. **Ongoing**: Monitor for new tools and updates to existing ones

Remember to regularly update tools and check for new MCP servers that might enhance your thesis writing workflow.
