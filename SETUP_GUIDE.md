# OpenCode Thesis Writing System - Setup Guide

This guide will help you set up the complete OpenCode-based thesis writing system for your Computer Science Bachelor thesis.

## Overview

The system includes:
- 8 specialized AI agents for different aspects of thesis writing
- Custom commands for thesis-specific tasks
- MCP servers for academic research and paper retrieval
- LaTeX document structure and templates
- Research organization and progress tracking tools

## Prerequisites

- Node.js 18+ and npm
- Git for version control
- LaTeX distribution (TeXLive or MiKTeX)
- Text editor or IDE
- Internet connection for MCP servers

## Quick Start (5 minutes)

1. **Clone or navigate to your project directory**
```bash
cd /path/to/your/thesis/project
```

2. **Verify the OpenCode configuration exists**
```bash
ls -la .opencode/
# Should show: opencode.json, AGENTS.md, prompts/, command/, tool/
```

3. **Install OpenCode CLI** (if not already installed)
```bash
npm install -g @opencode-ai/cli
```

4. **Test the configuration**
```bash
opencode agent list
opencode command list
```

5. **Start writing your thesis**
```bash
opencode
# Use Tab to switch between thesis-writer and research-coordinator agents
```

## Detailed Setup

### Step 1: System Requirements

#### Install Node.js and npm
```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# macOS
brew install node

# Windows
# Download from https://nodejs.org/
```

#### Install LaTeX Distribution
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install texlive-full

# macOS
brew install --cask mactex

# Windows
# Download MiKTeX from https://miktex.org/
```

### Step 2: OpenCode Installation

```bash
# Install OpenCode CLI
npm install -g @opencode-ai/cli

# Verify installation
opencode --version
```

### Step 3: API Keys Configuration

Create a `.env` file in your project root:
```bash
# .env
TAVILY_API_KEY=your_tavily_api_key_here
GITHUB_TOKEN=your_github_token_here  # for github-copilot models
```

**Getting API Keys:**
- **Tavily**: Sign up at https://tavily.com/ for web search capabilities
- **GitHub**: Generate token at https://github.com/settings/tokens for Copilot models

### Step 4: MCP Servers Installation

```bash
# Install ArXiv paper search
npm install -g @blazickjp/arxiv-mcp-server

# Install multi-source paper search
npm install -g @openags/paper-search-mcp

# Test installations
npx @blazickjp/arxiv-mcp-server --help
npx @openags/paper-search-mcp --help
```

### Step 5: Update Configuration

Edit `.opencode/opencode.json` and replace `YOUR_TAVILY_API_KEY` with your actual API key:
```json
{
  "mcp": {
    "tavily-search": {
      "type": "remote",
      "url": "https://api.tavily.com/mcp",
      "enabled": true,
      "headers": {
        "Authorization": "Bearer YOUR_ACTUAL_TAVILY_API_KEY"
      }
    }
  }
}
```

### Step 6: Test LaTeX Compilation

```bash
cd thesis/
pdflatex main.tex
# Should compile without errors (may show warnings about missing references)
```

## Using the System

### Available Agents

Switch between agents using Tab key or by typing their names:

1. **thesis-writer** - Primary writing agent
2. **research-coordinator** - Research management
3. **@literature-reviewer** - Paper analysis (subagent)
4. **@methodology-expert** - Research methods (subagent)
5. **@quality-assurance** - Quality review (subagent)
6. **@reference-manager** - Citations (subagent)
7. **@latex-specialist** - Formatting (subagent)
8. **@section-writer** - Section development (subagent)

### Custom Commands

Use these commands in the OpenCode interface:

- `/literature-search [topic]` - Comprehensive literature search
- `/thesis-outline [topic]` - Create detailed thesis outline
- `/compile-thesis` - Compile LaTeX and check for errors
- `/plagiarism-check [section]` - Check for plagiarism
- `/research [topic]` - Start research on a topic
- `/write-section [section]` - Write specific section
- `/review-quality [content]` - Quality review

### Custom Tools

Available through the tool system:

- `thesis-progress` - Track writing progress
- `citation-helper` - Generate and manage citations

### Example Workflow

1. **Start with research**:
```
/literature-search machine learning applications
```

2. **Create thesis outline**:
```
/thesis-outline "Machine Learning for Data Analysis"
```

3. **Write individual sections**:
```
/write-section introduction
```

4. **Check progress**:
```
Use thesis-progress tool with action "status"
```

5. **Compile and review**:
```
/compile-thesis
/review-quality introduction
```

## Directory Structure

After setup, your project should look like:

```
your-thesis-project/
├── .opencode/
│   ├── opencode.json          # Main configuration
│   ├── AGENTS.md              # Project rules
│   ├── prompts/               # Agent prompts
│   ├── command/               # Custom commands
│   └── tool/                  # Custom tools
├── thesis/
│   ├── main.tex               # Main LaTeX file
│   ├── sections/              # Individual sections
│   ├── figures/               # Images and diagrams
│   ├── references/            # Bibliography
│   └── .context/              # Research and metadata
├── NEEDED_MISSING_TOOLS.md    # Tools to install
├── SETUP_GUIDE.md             # This guide
└── .env                       # API keys (create this)
```

## Troubleshooting

### Common Issues

#### "Command not found: opencode"
```bash
# Reinstall OpenCode CLI
npm uninstall -g @opencode-ai/cli
npm install -g @opencode-ai/cli
```

#### "MCP server not responding"
```bash
# Check if servers are installed
npm list -g | grep mcp

# Reinstall if needed
npm install -g @blazickjp/arxiv-mcp-server
```

#### "LaTeX compilation failed"
```bash
# Check LaTeX installation
pdflatex --version

# Install missing packages
sudo apt-get install texlive-latex-extra texlive-fonts-recommended
```

#### "API key not working"
- Verify API key is correct in `.env` file
- Check API key permissions and quotas
- Ensure `.env` file is in project root

### Getting Help

1. **Check logs**: OpenCode provides detailed error messages
2. **Verify configuration**: Use `opencode config validate`
3. **Test components individually**: Test each MCP server separately
4. **Check documentation**: Refer to OpenCode docs at https://opencode.ai/docs/

## Advanced Configuration

### Custom Model Configuration

To use different models for specific agents, edit `.opencode/opencode.json`:

```json
{
  "agent": {
    "thesis-writer": {
      "model": "anthropic/claude-3-5-sonnet-20241022"
    },
    "research-coordinator": {
      "model": "openai/gpt-4-turbo"
    }
  }
}
```

### Performance Optimization

1. **Faster LaTeX compilation**:
   - Use `\includeonly{}` for partial compilation
   - Optimize figure formats and sizes

2. **Efficient research**:
   - Use specific search terms
   - Limit search scope when appropriate

3. **Better organization**:
   - Regular progress updates
   - Consistent file naming
   - Version control with Git

## Next Steps

1. **Immediate** (Today):
   - Complete setup and test basic functionality
   - Create your first thesis outline
   - Start initial literature search

2. **Week 1**:
   - Develop comprehensive research strategy
   - Write introduction draft
   - Set up regular progress tracking

3. **Ongoing**:
   - Regular quality reviews
   - Continuous research updates
   - Progress monitoring and adjustment

## Support and Resources

- **OpenCode Documentation**: https://opencode.ai/docs/
- **LaTeX Help**: https://www.latex-project.org/help/
- **Academic Writing**: University writing center resources
- **Technical Issues**: GitHub issues for specific tools

Remember: This system is designed to assist and enhance your thesis writing process. The AI agents provide guidance and support, but the final academic work and decisions remain yours.
