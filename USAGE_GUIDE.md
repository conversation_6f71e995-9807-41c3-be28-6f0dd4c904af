# OpenCode Thesis Writing System - Usage Guide

This guide explains how to effectively use the OpenCode-based thesis writing system with its specialized agents, commands, and tools.

## System Overview

The system consists of:
- **2 Primary Agents**: Main interfaces for thesis writing and research
- **6 Subagents**: Specialized experts for specific tasks
- **Custom Commands**: Thesis-specific workflows
- **Custom Tools**: Progress tracking and citation management
- **MCP Servers**: Academic research and paper retrieval

## Agent Architecture

### Primary Agents (Direct Interaction)

#### 1. `thesis-writer`
**Your main writing companion**
- Use for: Writing, editing, and integrating content
- Capabilities: Full thesis development, LaTeX expertise, content coordination
- When to use: Primary interface for most thesis work

#### 2. `research-coordinator` 
**Your research manager**
- Use for: Research planning, literature management, source organization
- Capabilities: Comprehensive research strategies, material organization
- When to use: Research planning, literature searches, research coordination

### Subagents (Call with @)

#### 3. `@literature-reviewer`
**Your paper analysis expert**
- Call with: `@literature-reviewer analyze this paper...`
- Specializes in: Detailed paper analysis, literature synthesis, research trends
- When to use: Deep paper analysis, literature review sections

#### 4. `@methodology-expert`
**Your research methods specialist**
- Call with: `@methodology-expert help design my experiment...`
- Specializes in: Research design, experimental planning, technical implementation
- When to use: Methodology sections, experimental design, validation

#### 5. `@quality-assurance`
**Your quality reviewer**
- Call with: `@quality-assurance review my introduction section...`
- Specializes in: Quality review, consistency checking, academic standards
- When to use: Content review, final checks, quality validation

#### 6. `@reference-manager`
**Your citation specialist**
- Call with: `@reference-manager create citation for this paper...`
- Specializes in: BibTeX management, citation formatting, reference organization
- When to use: Citation creation, bibliography management, reference formatting

#### 7. `@latex-specialist`
**Your formatting expert**
- Call with: `@latex-specialist fix this table formatting...`
- Specializes in: LaTeX formatting, document structure, compilation issues
- When to use: Formatting problems, LaTeX errors, document structure

#### 8. `@section-writer`
**Your section development specialist**
- Call with: `@section-writer write the results section...`
- Specializes in: Individual section writing, structure, consistency
- When to use: Focused section writing, section-specific requirements

## Typical Workflows

### 1. Starting Your Thesis

```bash
# Launch OpenCode
opencode

# Switch to research-coordinator
research-coordinator

# Plan your research
/literature-search "your research topic"

# Create thesis structure
/thesis-outline "Your Thesis Title"

# Switch to thesis-writer
thesis-writer

# Begin writing
Let's start writing the introduction section based on our research findings.
```

### 2. Literature Review Process

```bash
# Start with research coordination
research-coordinator

# Comprehensive literature search
/literature-search "machine learning applications in healthcare"

# Analyze specific papers
@literature-reviewer Please analyze these papers I found: [paper list]

# Synthesize findings
@literature-reviewer Create a synthesis of the current research trends

# Write the literature review
thesis-writer
Please write the literature review section based on our research findings.
```

### 3. Writing Individual Sections

```bash
# For methodology section
thesis-writer
@methodology-expert Help me design the experimental framework for my research

# For results section
@section-writer Write the results section based on our experimental data

# For discussion section
thesis-writer
Let's write the discussion section, interpreting our results in context
```

### 4. Quality Review Process

```bash
# Review specific content
@quality-assurance Please review my introduction section for academic quality

# Check citations
@reference-manager Validate all citations in the literature review

# Check formatting
@latex-specialist Review the document formatting and fix any issues

# Final compilation
/compile-thesis
```

### 5. Progress Tracking

```bash
# Check current progress
Use the thesis-progress tool with action "status"

# Update section progress
Use the thesis-progress tool with action "update", section "introduction", progress 75

# Get detailed report
Use the thesis-progress tool with action "summary"
```

## Custom Commands Reference

### `/literature-search [topic]`
**Purpose**: Comprehensive literature search and analysis
**Usage**: `/literature-search "neural networks for image classification"`
**Agent**: research-coordinator
**Output**: Research summary, key papers, research gaps

### `/thesis-outline [topic]`
**Purpose**: Create detailed thesis structure and outline
**Usage**: `/thesis-outline "Machine Learning for Medical Diagnosis"`
**Agent**: thesis-writer
**Output**: Complete thesis outline with sections and subsections

### `/compile-thesis`
**Purpose**: Compile LaTeX document and check for errors
**Usage**: `/compile-thesis`
**Agent**: latex-specialist
**Output**: Compilation status, error reports, suggestions

### `/plagiarism-check [section]`
**Purpose**: Check content for potential plagiarism
**Usage**: `/plagiarism-check introduction`
**Agent**: quality-assurance
**Output**: Plagiarism analysis, similarity reports, recommendations

### `/research [topic]`
**Purpose**: Start comprehensive research on specific topic
**Usage**: `/research "deep learning optimization techniques"`
**Agent**: research-coordinator
**Output**: Research strategy, source identification, material organization

### `/write-section [section]`
**Purpose**: Write specific thesis section
**Usage**: `/write-section methodology`
**Agent**: section-writer
**Output**: Complete section draft with proper structure

### `/review-quality [content]`
**Purpose**: Comprehensive quality review
**Usage**: `/review-quality "my results section"`
**Agent**: quality-assurance
**Output**: Quality assessment, improvement suggestions, compliance check

## Custom Tools Reference

### `thesis-progress`
**Purpose**: Track and report thesis writing progress
**Actions**:
- `status`: Show current progress overview
- `update`: Update progress for specific section
- `summary`: Detailed progress report

**Usage Examples**:
```
thesis-progress status
thesis-progress update introduction 80 "Completed first draft"
thesis-progress summary
```

### `citation-helper`
**Purpose**: Generate and manage BibTeX citations
**Actions**:
- `generate`: Create new BibTeX entry
- `validate`: Check bibliography for errors
- `search`: Find citations in bibliography
- `format`: Format citation for use

**Usage Examples**:
```
citation-helper generate "Paper Title" "Author Name" 2023
citation-helper validate
citation-helper search "machine learning"
```

## Best Practices

### 1. Agent Selection Strategy
- **Start broad**: Use primary agents (thesis-writer, research-coordinator)
- **Get specific**: Call subagents for specialized tasks
- **Stay organized**: Use research-coordinator for planning
- **Maintain quality**: Regular @quality-assurance reviews

### 2. Effective Communication
- **Be specific**: Provide clear context and requirements
- **Reference materials**: Point to existing research and sections
- **Set expectations**: Specify desired outcomes and formats
- **Iterate**: Use feedback to refine and improve

### 3. Research Management
- **Plan first**: Use /literature-search before writing
- **Document everything**: Save findings to .context/research/
- **Stay current**: Regular research updates and reviews
- **Organize sources**: Use @reference-manager consistently

### 4. Writing Process
- **Follow structure**: Use thesis outline as guide
- **Write iteratively**: Draft, review, revise cycle
- **Maintain consistency**: Regular style and terminology checks
- **Track progress**: Use thesis-progress tool regularly

### 5. Quality Assurance
- **Review early and often**: Don't wait until the end
- **Use multiple perspectives**: Different agents for different aspects
- **Check citations**: Validate references regularly
- **Compile frequently**: Catch LaTeX errors early

## Troubleshooting

### Common Issues

#### Agent Not Responding
```bash
# Check agent status
opencode agent list

# Restart if needed
opencode restart
```

#### LaTeX Compilation Errors
```bash
# Use the compile command
/compile-thesis

# Get specific help
@latex-specialist Fix this compilation error: [error message]
```

#### Research Not Finding Sources
```bash
# Try different search terms
/literature-search "alternative search terms"

# Use specific databases
@literature-reviewer Search ArXiv for papers on [topic]
```

#### Quality Issues
```bash
# Get comprehensive review
@quality-assurance Review this section for academic quality

# Check specific aspects
@quality-assurance Check citation accuracy in literature review
```

### Performance Tips

1. **Use specific agents**: Don't ask thesis-writer to do reference formatting
2. **Batch similar tasks**: Do all citations at once with @reference-manager
3. **Regular saves**: Commit progress frequently
4. **Incremental compilation**: Use LaTeX partial compilation for large documents
5. **Organized research**: Keep .context/ directory well-organized

## Advanced Usage

### Custom Workflows
You can create custom workflows by chaining commands and agents:

```bash
# Research → Write → Review workflow
/literature-search "topic"
@literature-reviewer Analyze top 10 papers
thesis-writer Write introduction based on research
@quality-assurance Review introduction for quality
```

### Integration with External Tools
- **Zotero**: Export to BibTeX, import with @reference-manager
- **Overleaf**: Sync LaTeX files for collaborative editing
- **Git**: Version control for thesis development
- **Grammarly**: External grammar checking before final review

## Getting Help

### Within OpenCode
- Ask any agent: "How should I approach [specific task]?"
- Use @quality-assurance for process guidance
- Check agent documentation in .opencode/agent/ directory

### External Resources
- OpenCode documentation: https://opencode.ai/docs/
- LaTeX help: https://www.latex-project.org/help/
- Academic writing resources: University writing centers
- Technical support: GitHub issues for specific tools

## Success Metrics

Track your success with:
- **Progress tracking**: Regular thesis-progress updates
- **Quality scores**: @quality-assurance assessments
- **Compilation success**: Error-free LaTeX compilation
- **Citation accuracy**: @reference-manager validation
- **Timeline adherence**: Meeting thesis milestones

Remember: The system is designed to support and enhance your thesis writing process. Use the agents as expert consultants, but maintain ownership of your academic work and final decisions.
