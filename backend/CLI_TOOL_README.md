# RDF Agent CLI Tool

A comprehensive command-line interface for querying use cases through the RDF Agent API. This tool provides both interactive and non-interactive modes for exploring RDF data across different use cases.

## Features

### 🎯 **Use Case Management**
- **Automatic Discovery**: Automatically discovers available use cases from the API
- **Interactive Selection**: Beautiful TUI for selecting and switching between use cases
- **Repository Information**: Shows triple counts and metadata for each use case
- **Session Management**: Maintains conversation context within each use case

### 💬 **Intelligent Querying**
- **Natural Language**: Ask questions in plain English
- **SPARQL Support**: Execute raw SPARQL queries with syntax highlighting
- **Context Awareness**: Maintains conversation history for follow-up questions
- **Rich Responses**: Formatted answers with query results, SPARQL queries, and sources

### 🎨 **Beautiful Interface**
- **Rich TUI**: Modern terminal interface with colors, tables, and panels
- **Progress Indicators**: Real-time feedback during API calls
- **Syntax Highlighting**: SPARQL queries displayed with proper syntax highlighting
- **Organized Output**: Structured display of results, metadata, and statistics

### 🔧 **Multiple Usage Modes**
- **Interactive Mode**: Full TUI experience for exploration
- **Single Query Mode**: Execute one query and exit
- **Command Mode**: Individual commands for scripting and automation
- **API Integration**: Full compatibility with all API endpoints

## Installation

The CLI tool is included with the RDF Agent backend. Ensure you have the required dependencies:

```bash
cd backend
uv add httpx typer rich python-dotenv
```

## Usage

### Interactive Mode (Recommended)

Start the interactive CLI:

```bash
uv run python cli_tool.py
```

This launches the full TUI experience where you can:
1. Select a use case from the available options
2. Ask questions in natural language
3. View formatted responses with results
4. Switch between use cases
5. Access help and examples

### Command Line Mode

Execute individual commands:

```bash
# List available use cases
uv run python cli_tool.py list-usecases

# Check system status
uv run python cli_tool.py status

# Show example queries
uv run python cli_tool.py examples

# Execute a single query
uv run python cli_tool.py query "How many buildings are there?"

# Execute a SPARQL query
uv run python cli_tool.py query "SELECT ?s ?p ?o WHERE { ?s ?p ?o } LIMIT 10" --sparql

# Query specific use case
uv run python cli_tool.py query "What types of data exist?" --use-case "PortfolioExample"
```

### Help and Documentation

```bash
# Show all available commands
uv run python cli_tool.py --help

# Show help for specific command
uv run python cli_tool.py query --help
```

## Interactive Commands

When in interactive mode, you can use these commands:

### Navigation Commands
- `select` or `s` - Select a different use case
- `status` or `info` - Show current system status
- `examples` or `ex` - Show example queries for current use case
- `help` or `?` - Show help message
- `quit`, `exit`, or `q` - Exit the application

### Query Commands
- Simply type your question in natural language
- Prefix with `sparql:` to execute raw SPARQL queries
- Examples:
  - `"How many buildings are there?"`
  - `"What cities are represented in the data?"`
  - `"sparql: SELECT ?s ?p ?o WHERE { ?s ?p ?o } LIMIT 10"`

## Configuration

### Environment Variables

Create a `.env` file in the backend directory:

```env
# API Configuration
API_BASE_URL=http://localhost:8000
OR_API_KEY=your_openrouter_api_key

# Optional: Custom timeout
API_TIMEOUT=30.0
```

### API URL Override

You can specify a different API URL for any command:

```bash
uv run python cli_tool.py status --api-url http://production-api:8000
```

## Example Session

```
🚀 RDF Agent CLI Tool
Interactive Use Case Querying System

🔍 Checking API connection...
✅ Connected to RDF Agent API

📚 Loading available use cases...
✅ Found 3 available use cases

🎯 Available Use Cases
┌───┬─────────────────┬─────────────────┬─────────┬───────────┐
│ # │ Use Case        │ Repository      │ Triples │ Status    │
├───┼─────────────────┼─────────────────┼─────────┼───────────┤
│ 1 │ PortfolioExample│ PortfolioExample│ 15,432  │ available │
│ 2 │ TestCase        │ TestCase        │ 1,234   │ available │
│ 3 │ SampleData      │ SampleData      │ 567     │ available │
└───┴─────────────────┴─────────────────┴─────────┴───────────┘

Select a use case (number) [1]: 1
✅ Selected use case: PortfolioExample

🎯 Active Use Case: PortfolioExample
Type 'help' for available commands, or ask a question directly

Query (PortfolioExample): How many buildings are there?

🤖 Processing query...
✅ Query completed in 2.34s

📊 Query Response
Query ID: abc123...
Processing Time: 2.34s
Confidence: 95%

💬 Answer
There are 1,247 buildings in the PortfolioExample dataset. This includes 
residential, commercial, and industrial buildings across multiple cities.

🔍 Generated SPARQL Query
1  PREFIX ibpdi: <https://ibpdi.datacat.org/class/>
2  SELECT (COUNT(*) as ?count) WHERE {
3      ?building a ibpdi:Building .
4  }

📋 Query Results (1 total)
┌───────┐
│ count │
├───────┤
│ 1247  │
└───────┘

Query (PortfolioExample): What cities are represented?

💬 Answer
The dataset contains buildings from 15 different cities, including:
- Frankfurt am Main (423 buildings)
- Berlin (312 buildings)
- Munich (198 buildings)
- Hamburg (156 buildings)
- And 11 other cities...

Query (PortfolioExample): quit
Goodbye! 👋
```

## API Integration

The CLI tool is fully integrated with the RDF Agent API and uses the following endpoints:

- **Health Check**: `GET /health`
- **Repositories**: `GET /api/v1/admin/repositories`
- **Repository Size**: `GET /api/v1/admin/repositories/{repo}/size`
- **Sessions**: `POST /api/v1/sessions/`
- **Queries**: `POST /api/v1/query/`
- **Examples**: `GET /api/v1/query/examples`
- **System Status**: `GET /api/v1/admin/status`

This ensures that any functionality available through the API is also available through the CLI.

## Testing

Run the comprehensive test suite:

```bash
uv run python test_cli_tool.py
```

This tests:
- API client functionality
- CLI components and display
- Query processing
- Error handling
- Command registration

## Troubleshooting

### Common Issues

1. **API Connection Failed**
   ```
   ❌ Cannot connect to API at http://localhost:8000
   ```
   - Ensure the API server is running: `uv run python main.py api`
   - Check the API URL in your `.env` file
   - Verify network connectivity

2. **No Use Cases Found**
   ```
   ⚠️ No use cases found
   ```
   - Ensure repositories exist in GraphDB
   - Check GraphDB connection in API
   - Verify repository permissions

3. **Query Timeout**
   ```
   ❌ Query failed or returned no response
   ```
   - Increase timeout in configuration
   - Check API logs for errors
   - Simplify complex queries

4. **Import Errors**
   ```
   ModuleNotFoundError: No module named 'rich'
   ```
   - Install dependencies: `uv add httpx typer rich python-dotenv`
   - Ensure you're in the correct virtual environment

### Debug Mode

For detailed debugging, set environment variables:

```bash
export DEBUG=1
export LOG_LEVEL=DEBUG
uv run python cli_tool.py
```

## Contributing

The CLI tool is designed to be extensible. Key areas for enhancement:

1. **Additional Commands**: Add new CLI commands in the `cli_tool.py` file
2. **Display Formats**: Enhance result formatting in the `format_query_response` method
3. **Export Options**: Add data export functionality
4. **Configuration**: Extend configuration options
5. **Plugins**: Add plugin system for custom functionality

## License

This CLI tool is part of the RDF Agent system and follows the same license terms.
