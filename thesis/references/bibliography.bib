% Bibliography file for Computer Science Bachelor Thesis
% Use IEEE citation style for Computer Science
% 
% BibTeX entry types commonly used in CS:
% @article - Journal articles
% @inproceedings - Conference papers
% @book - Books
% @incollection - Book chapters
% @techreport - Technical reports
% @misc - Web sources, software, datasets
% @phdthesis - PhD dissertations
% @mastersthesis - Master's theses

% Example entries - replace with your actual references

@article{example2023machine,
  author = {<PERSON> and <PERSON>},
  title = {Machine Learning Approaches for Data Analysis},
  journal = {IEEE Transactions on Pattern Analysis and Machine Intelligence},
  volume = {45},
  number = {3},
  pages = {123--135},
  year = {2023},
  doi = {10.1109/TPAMI.2023.1234567},
  note = {Example reference - replace with actual citations}
}

@inproceedings{example2022deep,
  author = {<PERSON> and <PERSON>},
  title = {Deep Learning for Computer Vision Applications},
  booktitle = {Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
  year = {2022},
  pages = {1234--1243},
  address = {New Orleans, LA, USA},
  publisher = {IEEE},
  doi = {10.1109/CVPR.2022.1234567},
  note = {Example reference - replace with actual citations}
}

@book{example2021algorithms,
  author = {<PERSON> and <PERSON> and <PERSON>. Rivest and <PERSON> <PERSON>},
  title = {Introduction to Algorithms},
  edition = {4th},
  publisher = {MIT Press},
  year = {2021},
  address = {Cambridge, MA, USA},
  isbn = {978-0262046305},
  note = {Example reference - replace with actual citations}
}

@incollection{example2023neural,
  author = {Sarah Brown},
  title = {Neural Network Architectures},
  booktitle = {Handbook of Artificial Intelligence},
  editor = {Michael Green and Lisa White},
  publisher = {Springer},
  year = {2023},
  pages = {45--78},
  address = {Berlin, Germany},
  doi = {10.1007/978-3-030-12345-6_3},
  note = {Example reference - replace with actual citations}
}

@misc{example2023dataset,
  author = {David Lee and Emma Davis},
  title = {Large-Scale Image Dataset for Machine Learning Research},
  howpublished = {Online},
  year = {2023},
  url = {https://example.com/dataset},
  note = {Accessed: 2024-01-15. Example reference - replace with actual citations}
}

@techreport{example2022technical,
  author = {Research Team},
  title = {Technical Report on Advanced Computing Methods},
  institution = {Computer Science Department, University of Example},
  year = {2022},
  number = {TR-2022-01},
  address = {Example City, State},
  note = {Example reference - replace with actual citations}
}

% Guidelines for creating BibTeX entries:
%
% 1. Use descriptive citation keys (AuthorYearKeyword format)
% 2. Include DOI when available for digital sources
% 3. Provide complete author names
% 4. Include page numbers for specific references
% 5. Use consistent formatting across all entries
% 6. Verify all information for accuracy
% 7. Include access dates for web sources
% 8. Use proper capitalization in titles
% 9. Include edition information for books
% 10. Provide complete publication information

% Common fields for different entry types:
%
% @article: author, title, journal, volume, number, pages, year, doi
% @inproceedings: author, title, booktitle, year, pages, address, publisher, doi
% @book: author, title, publisher, year, address, edition, isbn
% @incollection: author, title, booktitle, editor, publisher, year, pages, address, doi
% @misc: author, title, howpublished, year, url, note
% @techreport: author, title, institution, year, number, address

% Quality checklist for references:
% [ ] All entries have complete bibliographic information
% [ ] Citation keys follow consistent naming convention
% [ ] DOIs included where available
% [ ] URLs are stable and accessible
% [ ] Publication details are accurate
% [ ] Formatting is consistent across entries
% [ ] Sources meet academic quality standards
% [ ] Recent sources balanced with foundational works
% [ ] All cited sources are actually referenced in text
% [ ] No duplicate entries exist
